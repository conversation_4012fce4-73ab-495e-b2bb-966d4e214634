<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .game-container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            max-width: 400px;
            width: 100%;
        }
        
        h1 {
            margin-bottom: 20px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        input {
            padding: 15px;
            font-size: 18px;
            border: none;
            border-radius: 10px;
            margin: 10px;
            width: 150px;
            text-align: center;
        }
        
        button {
            padding: 15px 30px;
            font-size: 18px;
            background: #ff6b6b;
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            background: #ff5252;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .message {
            font-size: 20px;
            margin: 20px 0;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .attempts {
            font-size: 16px;
            margin: 10px 0;
        }
        
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .error {
            color: #ff6b6b;
            font-weight: bold;
        }
        
        .hint {
            color: #FFD700;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🎯 Sayı Tahmin Oyunu</h1>
        <p>1 ile 100 arasında bir sayı tuttum!</p>
        <p>Tahmin et bakalım!</p>
        
        <input type="number" id="guessInput" placeholder="Tahminin?" min="1" max="100">
        <br>
        <button onclick="checkGuess()">Tahmin Et!</button>
        <button onclick="newGame()">Yeni Oyun</button>
        
        <div class="message" id="message">Hadi başlayalım! 🚀</div>
        <div class="attempts" id="attempts">Deneme sayısı: 0</div>
    </div>

    <script>
        let randomNumber;
        let attemptCount;
        
        function newGame() {
            randomNumber = Math.floor(Math.random() * 100) + 1;
            attemptCount = 0;
            document.getElementById('message').innerHTML = 'Yeni oyun başladı! 🚀';
            document.getElementById('message').className = 'message';
            document.getElementById('attempts').innerHTML = 'Deneme sayısı: 0';
            document.getElementById('guessInput').value = '';
            document.getElementById('guessInput').focus();
        }
        
        function checkGuess() {
            const guess = parseInt(document.getElementById('guessInput').value);
            const messageElement = document.getElementById('message');
            const attemptsElement = document.getElementById('attempts');
            
            if (isNaN(guess) || guess < 1 || guess > 100) {
                messageElement.innerHTML = '❌ Lütfen 1-100 arası bir sayı girin!';
                messageElement.className = 'message error';
                return;
            }
            
            attemptCount++;
            attemptsElement.innerHTML = `Deneme sayısı: ${attemptCount}`;
            
            if (guess === randomNumber) {
                messageElement.innerHTML = `🎉 Tebrikler! ${attemptCount} denemede bildin!`;
                messageElement.className = 'message success';
            } else if (guess < randomNumber) {
                messageElement.innerHTML = '📈 Daha büyük bir sayı dene!';
                messageElement.className = 'message hint';
            } else {
                messageElement.innerHTML = '📉 Daha küçük bir sayı dene!';
                messageElement.className = 'message hint';
            }
            
            document.getElementById('guessInput').value = '';
            document.getElementById('guessInput').focus();
        }
        
        // Enter tuşu ile tahmin yapabilme
        document.getElementById('guessInput').addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                checkGuess();
            }
        });
        
        // Oyunu başlat
        newGame();
    </script>
</body>
</html>
